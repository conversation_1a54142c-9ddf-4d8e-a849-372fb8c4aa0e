#!/usr/bin/env python3
"""
Web Time Scraper - Capture time information from websites
Supports multiple methods to extract time data from web pages
"""

import requests
from bs4 import BeautifulSoup
import re
from datetime import datetime, timezone
import json
from urllib.parse import urljoin, urlparse
import time
import argparse


class WebTimeScraper:
    def __init__(self, timeout=10):
        """
        Initialize the web time scraper
        
        Args:
            timeout (int): Request timeout in seconds
        """
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def get_current_time_from_api(self, api_url="http://worldtimeapi.org/api/timezone/UTC"):
        """
        Get current time from a time API
        
        Args:
            api_url (str): URL of the time API
            
        Returns:
            dict: Time information from the API
        """
        try:
            response = self.session.get(api_url, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"Error fetching time from API: {e}")
            return None
    
    def extract_time_from_html(self, url):
        """
        Extract time information from HTML content
        
        Args:
            url (str): URL to scrape
            
        Returns:
            dict: Extracted time information
        """
        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            time_info = {
                'url': url,
                'scraped_at': datetime.now(timezone.utc).isoformat(),
                'times_found': []
            }
            
            # Look for common time-related elements
            time_selectors = [
                'time',
                '[datetime]',
                '.time',
                '.timestamp',
                '.date',
                '.datetime',
                '#time',
                '#timestamp',
                '#date'
            ]
            
            for selector in time_selectors:
                elements = soup.select(selector)
                for element in elements:
                    time_data = self._extract_time_from_element(element)
                    if time_data:
                        time_info['times_found'].append(time_data)
            
            # Search for time patterns in text content
            text_times = self._find_time_patterns_in_text(soup.get_text())
            time_info['times_found'].extend(text_times)
            
            return time_info
            
        except Exception as e:
            print(f"Error scraping time from {url}: {e}")
            return None
    
    def _extract_time_from_element(self, element):
        """
        Extract time information from a specific HTML element
        
        Args:
            element: BeautifulSoup element
            
        Returns:
            dict: Time information if found
        """
        time_data = {}
        
        # Check for datetime attribute
        if element.get('datetime'):
            time_data['datetime_attr'] = element.get('datetime')
        
        # Get element text
        text = element.get_text(strip=True)
        if text:
            time_data['text'] = text
            
            # Try to parse the text as a datetime
            parsed_time = self._parse_time_string(text)
            if parsed_time:
                time_data['parsed_datetime'] = parsed_time.isoformat()
        
        # Get element tag and attributes
        time_data['tag'] = element.name
        time_data['class'] = element.get('class', [])
        time_data['id'] = element.get('id', '')
        
        return time_data if any(key in time_data for key in ['datetime_attr', 'parsed_datetime']) else None
    
    def _find_time_patterns_in_text(self, text):
        """
        Find time patterns in plain text using regex
        
        Args:
            text (str): Text to search
            
        Returns:
            list: List of found time patterns
        """
        time_patterns = [
            # ISO 8601 format
            r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|[+-]\d{2}:\d{2})?',
            # Date formats
            r'\d{4}-\d{2}-\d{2}',
            r'\d{2}/\d{2}/\d{4}',
            r'\d{2}-\d{2}-\d{4}',
            # Time formats
            r'\d{1,2}:\d{2}:\d{2}(?:\s*[AP]M)?',
            r'\d{1,2}:\d{2}(?:\s*[AP]M)?',
            # Timestamp formats
            r'\d{10,13}',  # Unix timestamp
        ]
        
        found_times = []
        for pattern in time_patterns:
            matches = re.finditer(pattern, text, re.IGNORECASE)
            for match in matches:
                time_str = match.group()
                parsed_time = self._parse_time_string(time_str)
                if parsed_time:
                    found_times.append({
                        'text': time_str,
                        'pattern': pattern,
                        'parsed_datetime': parsed_time.isoformat(),
                        'position': match.span()
                    })
        
        return found_times
    
    def _parse_time_string(self, time_str):
        """
        Parse a time string into a datetime object
        
        Args:
            time_str (str): Time string to parse
            
        Returns:
            datetime: Parsed datetime object or None
        """
        # Common datetime formats to try
        formats = [
            '%Y-%m-%dT%H:%M:%S.%fZ',
            '%Y-%m-%dT%H:%M:%SZ',
            '%Y-%m-%dT%H:%M:%S',
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d',
            '%m/%d/%Y',
            '%d-%m-%Y',
            '%H:%M:%S',
            '%H:%M',
            '%I:%M:%S %p',
            '%I:%M %p',
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(time_str.strip(), fmt)
            except ValueError:
                continue
        
        # Try parsing as Unix timestamp
        try:
            timestamp = float(time_str)
            if len(time_str) == 13:  # Milliseconds
                timestamp = timestamp / 1000
            return datetime.fromtimestamp(timestamp, tz=timezone.utc)
        except (ValueError, OSError):
            pass
        
        return None
    
    def get_server_time(self, url):
        """
        Get server time from HTTP headers
        
        Args:
            url (str): URL to check
            
        Returns:
            dict: Server time information
        """
        try:
            response = self.session.head(url, timeout=self.timeout)
            
            server_time_info = {
                'url': url,
                'status_code': response.status_code,
                'headers': {}
            }
            
            # Extract time-related headers
            time_headers = ['date', 'last-modified', 'expires', 'if-modified-since']
            for header in time_headers:
                if header in response.headers:
                    server_time_info['headers'][header] = response.headers[header]
            
            return server_time_info
            
        except Exception as e:
            print(f"Error getting server time from {url}: {e}")
            return None


def main():
    """Main function to demonstrate usage"""
    parser = argparse.ArgumentParser(description='Capture time information from websites')
    parser.add_argument('url', nargs='?', help='URL to scrape time information from')
    parser.add_argument('--api', action='store_true', help='Get time from WorldTimeAPI')
    parser.add_argument('--server', action='store_true', help='Get server time from headers')
    parser.add_argument('--output', '-o', help='Output file to save results (JSON format)')
    
    args = parser.parse_args()
    
    scraper = WebTimeScraper()
    results = {}
    
    if args.api:
        print("Fetching time from WorldTimeAPI...")
        api_time = scraper.get_current_time_from_api()
        if api_time:
            results['api_time'] = api_time
            print(f"Current UTC time: {api_time.get('datetime', 'N/A')}")
    
    if args.url:
        print(f"Scraping time information from: {args.url}")
        
        # Extract time from HTML content
        html_time = scraper.extract_time_from_html(args.url)
        if html_time:
            results['html_time'] = html_time
            print(f"Found {len(html_time['times_found'])} time elements")
        
        if args.server:
            # Get server time
            server_time = scraper.get_server_time(args.url)
            if server_time:
                results['server_time'] = server_time
                print("Server time headers:", server_time.get('headers', {}))
    
    # Save results if output file specified
    if args.output and results:
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"Results saved to {args.output}")
    
    # Print results
    if results:
        print("\n" + "="*50)
        print("RESULTS:")
        print("="*50)
        print(json.dumps(results, indent=2, default=str))


if __name__ == "__main__":
    main()
