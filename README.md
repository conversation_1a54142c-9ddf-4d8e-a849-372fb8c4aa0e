# Web Time Scraper

A Python tool to capture time information from websites using multiple methods.

## Features

- **API Time Retrieval**: Get current time from time APIs (like WorldTimeAPI)
- **HTML Time Extraction**: Parse time information from HTML content using CSS selectors and regex patterns
- **Server Header Analysis**: Extract time-related information from HTTP response headers
- **Multiple Time Formats**: Supports various datetime formats including ISO 8601, Unix timestamps, and common date/time patterns

## Installation

1. Install the required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Command Line Interface

```bash
# Get time from WorldTimeAPI
python time_scraper.py --api

# Scrape time information from a website
python time_scraper.py https://time.is/

# Get server time headers
python time_scraper.py https://www.google.com --server

# Save results to a JSON file
python time_scraper.py https://time.is/ --output results.json

# Combine multiple methods
python time_scraper.py https://time.is/ --api --server --output complete_results.json
```

### Python API

```python
from time_scraper import WebTimeScraper

# Initialize the scraper
scraper = WebTimeScraper()

# Get current time from API
api_time = scraper.get_current_time_from_api()
print(f"Current UTC time: {api_time['datetime']}")

# Extract time from HTML
time_info = scraper.extract_time_from_html("https://time.is/")
print(f"Found {len(time_info['times_found'])} time elements")

# Get server headers
server_time = scraper.get_server_time("https://www.google.com")
print("Server headers:", server_time['headers'])
```

### Run Examples

```bash
python example_usage.py
```

## Methods

### WebTimeScraper Class

- `get_current_time_from_api(api_url)`: Fetch time from a time API
- `extract_time_from_html(url)`: Extract time information from HTML content
- `get_server_time(url)`: Get time-related HTTP headers
- `_find_time_patterns_in_text(text)`: Find time patterns using regex
- `_parse_time_string(time_str)`: Parse various time string formats

## Supported Time Formats

- ISO 8601: `2023-12-25T10:30:00Z`
- Unix timestamps: `1703505000`
- Date formats: `2023-12-25`, `12/25/2023`, `25-12-2023`
- Time formats: `10:30:00`, `10:30 AM`, `22:30`
- HTML datetime attributes
- HTTP date headers

## Output Format

The scraper returns structured data in JSON format containing:
- URL and timestamp of scraping
- Found time elements with their source and parsed values
- Server header information
- API response data

## Error Handling

The scraper includes robust error handling for:
- Network timeouts and connection errors
- Invalid HTML content
- Unparseable time formats
- Missing or malformed data

## Dependencies

- `requests`: HTTP library for web requests
- `beautifulsoup4`: HTML parsing library
- `lxml`: XML/HTML parser (optional but recommended for better performance)
