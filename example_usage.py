#!/usr/bin/env python3
"""
Example usage of the WebTimeScraper class
"""

from time_scraper import WebTimeScraper
import json


def example_time_api():
    """Example: Get current time from WorldTimeAPI"""
    print("=== Getting Current Time from API ===")
    scraper = WebTimeScraper()
    
    # Get UTC time
    utc_time = scraper.get_current_time_from_api("http://worldtimeapi.org/api/timezone/UTC")
    if utc_time:
        print(f"UTC Time: {utc_time['datetime']}")
        print(f"Timezone: {utc_time['timezone']}")
        print(f"Unix Timestamp: {utc_time['unixtime']}")
    
    # Get time for a specific timezone
    ny_time = scraper.get_current_time_from_api("http://worldtimeapi.org/api/timezone/America/New_York")
    if ny_time:
        print(f"New York Time: {ny_time['datetime']}")


def example_html_scraping():
    """Example: Scrape time information from HTML pages"""
    print("\n=== Scraping Time from HTML Pages ===")
    scraper = WebTimeScraper()
    
    # Example URLs that might contain time information
    test_urls = [
        "https://time.is/",
        "https://www.timeanddate.com/worldclock/",
        "https://www.worldtimeserver.com/"
    ]
    
    for url in test_urls:
        print(f"\nScraping: {url}")
        try:
            time_info = scraper.extract_time_from_html(url)
            if time_info and time_info['times_found']:
                print(f"Found {len(time_info['times_found'])} time elements:")
                for i, time_elem in enumerate(time_info['times_found'][:3]):  # Show first 3
                    print(f"  {i+1}. {time_elem.get('text', 'N/A')} -> {time_elem.get('parsed_datetime', 'Could not parse')}")
            else:
                print("  No time information found")
        except Exception as e:
            print(f"  Error: {e}")


def example_server_headers():
    """Example: Get server time from HTTP headers"""
    print("\n=== Getting Server Time from Headers ===")
    scraper = WebTimeScraper()
    
    test_urls = [
        "https://www.google.com",
        "https://www.github.com",
        "https://httpbin.org/headers"
    ]
    
    for url in test_urls:
        print(f"\nChecking server headers: {url}")
        server_time = scraper.get_server_time(url)
        if server_time and server_time.get('headers'):
            for header, value in server_time['headers'].items():
                print(f"  {header}: {value}")
        else:
            print("  No time-related headers found")


def example_custom_website():
    """Example: Scrape time from a custom website"""
    print("\n=== Custom Website Example ===")
    scraper = WebTimeScraper()
    
    # You can replace this with any website you want to test
    url = "https://httpbin.org/json"  # This returns JSON with timestamp
    
    print(f"Analyzing: {url}")
    
    # Get HTML time information
    html_time = scraper.extract_time_from_html(url)
    if html_time:
        print("HTML Time Analysis:")
        print(json.dumps(html_time, indent=2, default=str))
    
    # Get server headers
    server_time = scraper.get_server_time(url)
    if server_time:
        print("\nServer Headers:")
        print(json.dumps(server_time, indent=2, default=str))


if __name__ == "__main__":
    print("Web Time Scraper Examples")
    print("=" * 40)
    
    # Run examples
    example_time_api()
    example_html_scraping()
    example_server_headers()
    example_custom_website()
    
    print("\n" + "=" * 40)
    print("Examples completed!")
